# 项目清理操作清单

## 执行前准备

### 1. 备份操作
- [ ] 创建项目完整备份
- [ ] 创建Git分支: `git checkout -b cleanup-2025-06-19`
- [ ] 确认当前工作目录干净: `git status`

### 2. 环境检查
- [ ] 确认Java环境: `java -version`
- [ ] 确认Maven环境: `mvn -version`
- [ ] 确认项目可正常编译: `mvn compile`

## 阶段1: 安全清理 (低风险)

### 1.1 删除重复文件
- [ ] 删除 `src/main/java/com/wbgame/MyCommandLineRunner2.java`
  ```bash
  rm src/main/java/com/wbgame/MyCommandLineRunner2.java
  ```

### 1.2 清理pom.xml注释代码
- [ ] 删除第57-61行注释掉的pagehelper依赖
- [ ] 删除第107-111行注释掉的bouncycastle依赖
- [ ] 删除第191-195行注释掉的oracle依赖
- [ ] 删除第202-211行注释掉的swagger依赖
- [ ] 删除第339-353行注释掉的javacv依赖

### 1.3 清理Java文件中的注释代码
- [ ] `AdController.java` 清理第1620-1622行注释掉的OSS配置
- [ ] `CashUpdateTask.java` 清理第93行注释掉的定时任务
- [ ] `WbSysController.java` 清理第275-284行注释掉的token验证代码

### 1.4 处理TODO标记
- [ ] `SomeServiceImpl.java:88` - 完善TODO方法实现或删除
- [ ] `UsertagTask.java:856` - 添加适当的异常处理
- [ ] `GameTask.java:894` - 完善TODO注释或移除

### 1.5 清理未使用的导入
- [ ] 运行IDE的"优化导入"功能
- [ ] 手动检查并清理明显未使用的import语句

## 阶段2: 依赖优化 (中风险)

### 2.1 分析未使用的依赖
需要验证以下依赖是否真正使用:
- [ ] `jcodec` (第129-137行) - 视频处理库
- [ ] `thumbnailator` (第138-142行) - 图片处理库  
- [ ] `jave` (第289-293行) - 音视频转换库
- [ ] `webp-imageio` (第361-365行) - WebP图片处理
- [ ] `commons-httpclient` (第252-256行) - HTTP客户端(可能被httpclient替代)

### 2.2 解决重复依赖
- [ ] 检查aliyun-java-sdk-core重复声明 (第167行和第302行)
- [ ] 检查aliyun-sdk-oss重复声明 (第170行和第332行)
- [ ] 统一druid版本 (第73行和第238行)

### 2.3 版本升级建议
- [ ] Spring Boot 1.5.9 → 2.x (需要兼容性测试)
- [ ] MySQL Connector版本统一
- [ ] 其他过时依赖版本检查

## 阶段3: 配置外部化 (中风险)

### 3.1 移除硬编码配置
需要移到配置文件的硬编码值:

#### OSS配置 (AdController.java)
```java
// 当前硬编码
String accessKeyId = "LTAI5tAWSWUTUQaGVDDArFMq";
String accessKeySecret = "******************************";
String bucketName = "dnwx-res";
```
- [ ] 移动到application.yml
- [ ] 创建OSSConfig配置类
- [ ] 使用@ConfigurationProperties

#### API密钥 (MyCommandLineRunner.java)
```java
// 当前硬编码
public static String accessKey = "1617679647909641842";
public static String accessSecret = "2b4ef9bc48e0b45cf66a71a4b331b868dc59f88b4274472473e0516868ca73df";
```
- [ ] 移动到配置文件
- [ ] 使用环境变量或加密配置

### 3.2 创建配置类
- [ ] 创建 `OSSConfigProperties.java`
- [ ] 创建 `ApiConfigProperties.java`
- [ ] 更新相关使用的类

## 阶段4: 代码重构 (高风险)

### 4.1 工具类优化
- [ ] 分析StringUtils工具类使用情况
- [ ] 合并重复的工具方法
- [ ] 统一常量定义

### 4.2 服务类优化
- [ ] 检查未使用的Service方法
- [ ] 合并重复的业务逻辑
- [ ] 优化数据库查询

### 4.3 控制器优化
- [ ] 清理未使用的Controller方法
- [ ] 统一返回值格式
- [ ] 优化异常处理

## 验证测试

### 编译测试
- [ ] `mvn clean compile` - 编译通过
- [ ] `mvn test` - 单元测试通过
- [ ] `mvn package` - 打包成功

### 功能测试
- [ ] 应用启动正常
- [ ] 主要API接口正常
- [ ] 数据库连接正常
- [ ] Redis连接正常

### 性能测试
- [ ] 启动时间对比
- [ ] 内存使用对比
- [ ] 构建时间对比

## 具体操作命令

### Git操作
```bash
# 创建清理分支
git checkout -b cleanup-2025-06-19

# 提交每个阶段的更改
git add .
git commit -m "阶段1: 安全清理完成"

# 推送分支
git push origin cleanup-2025-06-19
```

### Maven操作
```bash
# 清理编译
mvn clean compile

# 运行测试
mvn test

# 打包应用
mvn package

# 依赖分析
mvn dependency:analyze
mvn dependency:tree
```

### 文件操作
```bash
# 删除重复文件
rm src/main/java/com/wbgame/MyCommandLineRunner2.java

# 查找TODO标记
grep -r "TODO" src/main/java/

# 查找FIXME标记  
grep -r "FIXME" src/main/java/

# 查找硬编码的密钥
grep -r "accessKey" src/main/java/
```

## 回滚计划

### 如果出现问题
```bash
# 回滚到清理前状态
git checkout main
git branch -D cleanup-2025-06-19

# 或者回滚到特定提交
git reset --hard <commit-hash>
```

### 分阶段回滚
- 每个阶段都有独立的提交
- 可以回滚到任意阶段
- 保留有用的清理成果

## 完成检查

### 代码质量
- [ ] 无编译错误
- [ ] 无明显的代码异味
- [ ] 注释和文档更新
- [ ] 代码格式统一

### 功能完整性
- [ ] 所有原有功能正常
- [ ] 配置正确加载
- [ ] 数据库操作正常
- [ ] 定时任务正常

### 性能指标
- [ ] 启动时间无明显增加
- [ ] 内存使用无明显增加
- [ ] 构建时间有所减少

## 文档更新

### 需要更新的文档
- [ ] README.md - 更新项目说明
- [ ] 配置文档 - 新增配置说明
- [ ] 部署文档 - 更新部署步骤
- [ ] API文档 - 确认接口无变化

### 清理记录
- [ ] 更新 `task-execution-log.md`
- [ ] 记录清理的具体内容
- [ ] 记录遇到的问题和解决方案
- [ ] 记录性能改进数据

## 注意事项

1. **安全第一**: 每个操作前都要确认影响范围
2. **小步快跑**: 每次只做一小部分更改并测试
3. **及时提交**: 每个阶段完成后及时提交代码
4. **保留备份**: 重要配置和代码要保留备份
5. **团队沟通**: 重大更改要与团队成员确认
