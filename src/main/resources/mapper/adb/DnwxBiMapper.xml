<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wbgame.mapper.adb.DnwxBiMapper">

	<insert id="batchExecSql" parameterType="java.util.Map">
		${sql1}
		<foreach collection="list" item="li" separator=",">
			${sql2}
		</foreach>
		${sql3}
	</insert>
	<update id="batchExecSqlTwo" parameterType="java.util.Map">
		<foreach collection="list" item="li" separator=";">
			${sql1}
		</foreach>
	</update>

	<insert id="insertExtendAdtypeRevise" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_revise_adtype(tdate,appid,cha_id,cha_type_name,cha_media,actnum,addnum,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,aa.cha_type_name,aa.cha_media,bb.act_num,bb.add_num,SUM(aa.revise_revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa LEFT JOIN data_ym.umeng_user_channel_total bb
		ON aa.tdate=bb.tdate and aa.appid=bb.appid and aa.cha_id=bb.install_channel

		where aa.tdate = '${tdate}' and aa.revise_show is not null
		and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id
	</insert>
	<insert id="insertExtendAdtypeReviseTwo" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_revise_adtype_two(tdate,appid,cha_id,cha_type_name,cha_media,actnum,addnum,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,aa.cha_type_name,aa.cha_media,bb.act_users actnum,bb.new_users addnum,SUM(aa.revise_revenue) revenue,
			   SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			   SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			   SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			   SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			   SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			   SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			   SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			   SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			   SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			   SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa LEFT JOIN dnwx_bi.ads_dim_users_info_3d_hourly bb
		ON aa.tdate=bb.tdate and aa.appid=bb.appid and aa.cha_id=bb.download_channel

		where aa.tdate = '${tdate}' and aa.revise_show is not null
		  and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id
	</insert>

	<insert id="insertExtendAdtypeGroup" parameterType="java.lang.String">
		REPLACE INTO dnwx_bi.dn_extend_group_adtype(tdate,appid,cha_id,prjid,user_group,actnum,addnum,sum_revenue,
			pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.cha_id,aa.prjid,aa.user_group,aa.actnum,aa.addnum,SUM(aa.revise_revenue) revenue,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa
		where aa.tdate = '${tdate}' and aa.revise_show is not null
		and SUBSTRING_INDEX(aa.adsid,'_',1) not in ('Mjuhe','bxm')
		group by aa.appid,aa.cha_id,aa.prjid,aa.user_group
	</insert>

	<insert id="insertExtendPrjidIncome" parameterType="java.lang.String">
		INSERT INTO dnwx_bi.dn_extend_prjid_income(tdate,appid,prjid,cha_id,cha_type_name,cha_media,actnum,addnum,sum_revenue,pv_splash,pv_plaque,pv_banner,pv_video,pv_msg,revenue_splash,revenue_plaque,revenue_banner,revenue_video,revenue_msg)

		select '${tdate}' tdate,aa.appid,aa.prjid,aa.cha_id,aa.cha_type_name,aa.cha_media,bb.act_users actnum,bb.new_users addnum,SUM(aa.revise_revenue) revenue,
			   SUM(CASE WHEN adpos_type = 'splash' THEN revise_show ELSE 0 END) pv_splash,
			   SUM(CASE WHEN adpos_type = 'plaque' THEN revise_show ELSE 0 END) pv_plaque,
			   SUM(CASE WHEN adpos_type = 'banner' THEN revise_show ELSE 0 END) pv_banner,
			   SUM(CASE WHEN adpos_type = 'video' THEN revise_show ELSE 0 END) pv_video,
			   SUM(CASE WHEN adpos_type = 'msg' THEN revise_show ELSE 0 END) pv_msg,
			   SUM(CASE WHEN adpos_type = 'splash' THEN revise_revenue ELSE 0 END) revenue_splash,
			   SUM(CASE WHEN adpos_type = 'plaque' THEN revise_revenue ELSE 0 END) revenue_plaque,
			   SUM(CASE WHEN adpos_type = 'banner' THEN revise_revenue ELSE 0 END) revenue_banner,
			   SUM(CASE WHEN adpos_type = 'video' THEN revise_revenue ELSE 0 END) revenue_video,
			   SUM(CASE WHEN adpos_type = 'msg' THEN revise_revenue ELSE 0 END) revenue_msg
		from dnwx_bi.dn_extend_revise_income aa LEFT JOIN dnwx_bi.ads_dim_users_info_4d_hourly bb
		ON aa.tdate=bb.tdate and aa.prjid=bb.pid and aa.cha_id=bb.download_channel

		where aa.tdate = '${tdate}'
		group by aa.prjid,aa.cha_id
	</insert>



	<!-- 变现-数据gap统计  -->
  	<select id="selectDnShowGapTotal" parameterType="java.util.Map" resultType="java.util.Map">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
		request_count,
		return_count,
		pv,
		click,

		req_num,
		fill_num,
		selfshow_num,
		click_num,
		show_num as showed_num,
		CONCAT(IFNULL(TRUNCATE(show_num/selfshow_num*100, 2), 0.00), '%') showed_rate,

		CONCAT(IFNULL(TRUNCATE((s_click_rate - click_rate)/s_click_rate*100, 2), 0.00), '%') click_filter_rate,

		CONCAT(p_fill_rate, '%') p_fill_rate,
		CONCAT(p_expose_rate, '%') p_expose_rate,
		CONCAT(s_fill_rate, '%') s_fill_rate,
		CONCAT(s_expose_rate, '%') s_expose_rate,
		
		CONCAT(s_click_rate, '%') s_click_rate,
		CONCAT(click_rate, '%') click_rate,

		CONCAT(req_gap, '%') req_gap,
		CONCAT(return_gap, '%') return_gap,
		CONCAT(pv_gap, '%') pv_gap,
		CONCAT(click_gap, '%') click_gap
		from (<include refid="dn_showgap_sql"/>) t
	</select>
	<select id="selectDnShowGapTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
		CONCAT(TRUNCATE(sum(xx.return_count)/sum(xx.request_count)*100, 2),'%') p_fill_rate,
		CONCAT(TRUNCATE(sum(xx.pv)/sum(xx.return_count)*100, 2),'%') p_expose_rate,
		CONCAT(TRUNCATE(sum(xx.fill_num)/sum(xx.req_num)*100, 2),'%') s_fill_rate,
		CONCAT(TRUNCATE(sum(xx.selfshow_num)/sum(xx.fill_num)*100, 2),'%') s_expose_rate,
		CONCAT(TRUNCATE(sum(click_num)/sum(selfshow_num)*100,2),'%') s_click_rate,
		CONCAT(TRUNCATE(sum(click)/sum(pv)*100,2),'%') click_rate,
		CONCAT(TRUNCATE((sum(s_click_rate) - sum(click_rate))/sum(s_click_rate)*100,2), '%') click_filter_rate,
			SUM(xx.request_count) request_count,
			SUM(xx.req_num) req_num,
			CONCAT(TRUNCATE((sum(xx.req_num)-sum(xx.request_count))/sum(xx.req_num)*100, 1),'%') req_gap,

			SUM(xx.return_count) return_count,
			SUM(xx.fill_num) fill_num,
			CONCAT(TRUNCATE((sum(xx.fill_num)-sum(xx.return_count))/sum(xx.fill_num)*100, 1),'%') return_gap,

			SUM(xx.pv) pv,
			SUM(xx.selfshow_num) selfshow_num,
			CONCAT(TRUNCATE((sum(xx.selfshow_num)-sum(xx.pv))/sum(xx.selfshow_num)*100, 1),'%') pv_gap,

			SUM(xx.show_num) as showed_num,
			CONCAT(IFNULL(TRUNCATE(SUM(xx.show_num)/SUM(xx.selfshow_num)*100, 2), 0.00), '%') showed_rate,

			SUM(xx.click) click,
			SUM(xx.click_num) click_num,
			CONCAT(TRUNCATE((sum(xx.click_num)-sum(xx.click))/sum(xx.click_num)*100, 1),'%') click_gap
		from (<include refid="dn_showgap_sql"/>) xx
	</select>
	<sql id="dn_showgap_sql">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
		IFNULL(SUM(request_count),0) request_count,
		IFNULL(SUM(return_count),0) return_count,
		IFNULL(SUM(pv),0) pv,
		IFNULL(SUM(click),0) click,
		IFNULL(SUM(req_num),0) req_num,
		IFNULL(SUM(fill_num),0) fill_num,
		IFNULL(SUM(selfshow_num),0) selfshow_num,
		IFNULL(SUM(click_num),0) click_num,
		IFNULL(SUM(show_num),0) show_num,

		IFNULL(TRUNCATE(sum(return_count)/sum(request_count)*100,2),0) p_fill_rate,
		IFNULL(TRUNCATE(sum(pv)/sum(return_count)*100,2),0) p_expose_rate,
		IFNULL(TRUNCATE(sum(fill_num)/sum(req_num)*100,2),0) s_fill_rate,
		IFNULL(TRUNCATE(sum(selfshow_num)/sum(fill_num)*100,2),0) s_expose_rate,
		
		IFNULL(TRUNCATE(sum(click_num)/sum(selfshow_num)*100,2),0) s_click_rate,
		IFNULL(TRUNCATE(sum(click)/sum(pv)*100,2),0) click_rate,

		IFNULL(TRUNCATE((sum(req_num)-sum(request_count))/sum(req_num)*100,1),0) req_gap,
		IFNULL(TRUNCATE((sum(fill_num)-sum(return_count))/sum(fill_num)*100,1),0) return_gap,
		IFNULL(TRUNCATE((sum(selfshow_num)-sum(pv))/sum(selfshow_num)*100,1),0) pv_gap,
		IFNULL(TRUNCATE((sum(click_num)-sum(click))/sum(click_num)*100,1),0) click_gap
		from ${tableName}
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="sdk_adtype != null and sdk_adtype != ''">
			and sdk_adtype = #{sdk_adtype}
		</if>
		<if test="open_type != null and open_type != ''">
			and open_type = #{open_type}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid in (${adsid})
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
		<if test="channel != null and channel != ''">
			and channel in (${channel})
		</if>
		<if test="out != null and out != ''">
			and `out` = #{out}
		</if>
		<if test="adconfig != null and adconfig != ''">
			and FIND_IN_SET(adsid,#{adconfig})
		</if>
		<if test="appid_tag != null and appid_tag != ''">
			<choose>
				<when test="appid_tag_rev != null and appid_tag_rev != ''">
					AND CONCAT(appid,'#',channel) not in (${appid_tag})
				</when>
				<otherwise>
					AND CONCAT(appid,'#',channel) in (${appid_tag})
				</otherwise>
			</choose>
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,pv desc
			</otherwise>
		</choose>

	</sql>

	<!-- 变现收入预估  -->
	<select id="selectDnExIncomeData" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_exincome_sql"/>
	</select>
	<select id="selectDnExIncomeDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			TRUNCATE(SUM(xx.income), 2) income,
			TRUNCATE(SUM(xx.income)/SUM(xx.show_num)*1000, 2) ecpm,
			CONCAT(ROUND(sum(xx.fill_num)/sum(xx.req_num)*100, 2),'%') fill_rate,
			CONCAT(ROUND(sum(xx.show_num)/sum(xx.fill_num)*100, 2),'%') show_rate,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate
		from (<include refid="dn_exincome_sql"/>) xx
	</select>
	<sql id="dn_exincome_sql">
		SELECT xx.* FROM
		(select
			<if test="group != null and group != ''">
				${group},
			</if>
			IFNULL(aa.temp_id,'') temp_id,
			IFNULL(aa.temp_name,'') temp_name,
			bb.strategy,
			concat(tdate,'') date,
			agent,
			cha_type_name,
			cha_media,
			IFNULL(SUM(act_num),0) act_num,
			IFNULL(SUM(add_num),0) add_num,
			SUM(aa.req_num) req_num,
			SUM(aa.fill_num) fill_num,
			SUM(aa.show_num) show_num,
			SUM(aa.click_num) click_num,
			IFNULL(TRUNCATE(SUM(aa.income), 2), 0) income,
			IFNULL(TRUNCATE(SUM(aa.income)/SUM(aa.show_num)*1000, 2), 0) ecpm,
			IFNULL(TRUNCATE(sum(aa.fill_num)/sum(aa.req_num)*100, 2),0) fill_rate,
			IFNULL(TRUNCATE(sum(aa.show_num)/sum(aa.fill_num)*100, 2),0) show_rate,
			IFNULL(TRUNCATE(sum(aa.click_num)/sum(aa.show_num)*100, 2),0) click_rate,
			TRUNCATE(estimated_ecpm,2) estimated_ecpm

			from ${tableName} aa LEFT JOIN dnwx_bi.dn_extend_adsid_list bb ON aa.adsid=bb.sid
			where tdate BETWEEN #{start_date} AND #{end_date}
			<if test="appid != null and appid != ''">
				and appid in (${appid})
			</if>
			<if test="cha_id != null and cha_id != ''">
				and cha_id in (${cha_id})
			</if>
			<if test="prjid != null and prjid != ''">
				and prjid = #{prjid}
			</if>
			<if test="adsid != null and adsid != ''">
				and adsid like concat('%',#{adsid},'%')
			</if>
			<if test="adpos_type != null and adpos_type != ''">
				and adpos_type = #{adpos_type}
			</if>
			<if test="sdk_adtype != null and sdk_adtype != ''">
				and sdk_adtype = #{sdk_adtype}
			</if>
			<if test="user_group != null and user_group != ''">
				and user_group like concat('%',#{user_group},'%')
			</if>
			<if test="agent != null and agent != ''">
				and agent = #{agent}
			</if>
			<if test="cha_type_name != null and cha_type_name != ''">
				and cha_type_name in (${cha_type_name})
			</if>
			<if test="strategy != null and strategy != ''">
				and bb.strategy = #{strategy}
			</if>
			<if test="apps != null and apps != ''">
				and appid in (${apps})
			</if>
			<if test="temp_id != null and temp_id != ''">
				and temp_id in (${temp_id})
			</if>
			<if test="temp_name != null and temp_name != ''">
				and temp_name like concat('%',#{temp_name},'%')
			</if>

			<if test="group != null and group != ''">
				group by ${group}
			</if> ) xx
		<if test="order_str != null and order_str != ''">
		 	order by ${order_str}
		</if>
	</sql>

	<!-- 变现收入校准  -->
	<select id="selectExtendIncomeRevise" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_income_sql"/>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,self_show desc
			</otherwise>
		</choose>
	</select>
	<select id="selectExtendIncomeReviseSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
		SUM(xx.self_show) self_show,
		SUM(xx.revise_show) revise_show,
		SUM(xx.revise_revenue) revise_revenue
		from (<include refid="dn_revise_income_sql"/>) xx
	</select>
	<sql id="dn_revise_income_sql">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
		sdk_adtype,
		IFNULL(TRUNCATE(sum(concat(gap_val, '')), 0), 0) gap_val,
		IFNULL(TRUNCATE(sum(concat(self_show, '')), 0), 0) self_show,
		IFNULL(TRUNCATE(sum(concat(revise_show, '')), 0), 0) revise_show,
		IFNULL(TRUNCATE(sum(concat(revise_revenue, '')), 2), 0) revise_revenue,
		IFNULL(TRUNCATE(sum(revise_revenue)/(sum(revise_show)) * 1000, 2), 0) ecpm,
		IFNULL(sum(actnum), 0) actnum,
		IFNULL(sum(addnum), 0) addnum
		from (
		select
		tdate,appid,cha_id,prjid,adsid,adpos_type,user_group,
		sdk_adtype,
		ecpm,
		gap_val,
		self_show,
		TRUNCATE(self_show*(1-gap_val*0.01),0) revise_show,
		TRUNCATE(ecpm*(self_show*(1-gap_val*0.01))/1000, 2) revise_revenue,
		actnum,
		addnum,
		cha_media,
		cha_type_name,
		agent
		from ${tableName}
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent}
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid in (${adsid})
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type}
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group like concat('%',#{user_group},'%')
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		group by tdate,appid,cha_id,prjid,adsid,adpos_type,user_group
		)
		group by ${group}
	</sql>

	<!-- 汇总数据校准  -->
  	<select id="selectAdtypeTotalRevise" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_adtype_sql"/>
	</select>
	<select id="selectAdtypeTotalReviseSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS duration,

			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,

			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.add_revenue) / SUM(xx.addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_revise_adtype_sql"/>) xx
	</select>
	<sql id="dn_revise_adtype_sql">
		select
		<if test="group != null and group != ''">
			${group},CONCAT(${group}) mapkey,
		</if>
		temp_id,temp_name,
		IFNULL(SUM(actnum),0) actnum,
		IFNULL(SUM(addnum),0) addnum,
		sec_TO_time(cast(avg(TIME_TO_SEC(duration)) as SIGNED)) AS duration,

		IFNULL(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),0) addrate,
		IFNULL(TRUNCATE(SUM(sum_revenue),2),0) sum_revenue,
		IFNULL(TRUNCATE(SUM(concat(add_revenue,'')),2),0) add_revenue,


			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(concat(add_revenue,'')) / SUM(addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg

		from ${tableName} a left join
		dim_cha_function_info b on a.tdate = b.f_tdate and a.appid = b.f_appid and a.cha_id = b.f_cha_id
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
		<if test="two_apps != null and two_apps != ''">
			and appid in (${two_apps})
		</if>
		<if test="dau != null and dau != ''">
			and IFNULL(actnum,0) = ${dau}
		</if>
		<if test="temp_id != null and temp_id != '' ">
			and temp_id like concat('%',#{temp_id},'%')
		</if>
		<if test="temp_name != null and temp_name != '' ">
			and temp_name like concat('%',#{temp_name},'%')
		</if>

		<if test="group != null and group != ''">
			  group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,sum_revenue desc
			</otherwise>
		</choose>
	</sql>


	<!-- 汇总数据校准-新增用户arpu  -->
	<select id="selectAdtypeTotalReviseV3" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_revise_adtype_v3_sql"/>
	</select>
	<select id="selectAdtypeTotalReviseV3Sum" parameterType="java.util.Map" resultType="java.util.Map">
		select
		SUM(xx.actnum) actnum,
		SUM(xx.addnum) addnum,
		CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
		TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,

		IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
		IFNULL(TRUNCATE(SUM(xx.add_revenue) / SUM(xx.addnum), 2),0) add_arpu,


		IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
		IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
		IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
		IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
		IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

		IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
		IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
		IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
		IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
		IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

		IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
		IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
		IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
		IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
		IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

		IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
		IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
		IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
		IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
		IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

		IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
		IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
		IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
		IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
		IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_revise_adtype_v3_sql"/>) xx
	</select>
	<sql id="dn_revise_adtype_v3_sql">
		select
		<if test="group != null and group != ''">
			${group},
		</if>
		SUM(actnum) actnum,
		SUM(addnum) addnum,
		CONCAT(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),'%') addrate,
		TRUNCATE(SUM(sum_revenue),2) sum_revenue,
		TRUNCATE(SUM(b.add_revenue)/100,2) add_revenue,
		IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
		IFNULL(TRUNCATE(SUM(b.add_revenue)/100 / SUM(addnum), 2),0) add_arpu,


		IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
		IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
		IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
		IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
		IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
		IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
		IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

		IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
		IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
		IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
		IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
		IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
		IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
		IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
		IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
		IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg

		from dn_extend_revise_adtype_two a
		left join
			(select a_day, appid AS app, channel, channeltype, media, add_revenue from `ads_add_user_arpu_daily`
				where a_day BETWEEN #{sdate} AND #{edate} ) b
			ON a.tdate=b.a_day AND a.appid=b.app AND a.cha_id=b.channel AND a.cha_type_name=b.channeltype AND a.cha_media=b.media

		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
		<if test="two_apps != null and two_apps != ''">
			and appid in (${two_apps})
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>

		order by tdate desc,actnum desc
	</sql>


	<!-- 项目ID收入预估  -->
  	<select id="selectPrjidTotalIncome" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_prjid_income_sql"/>
	</select>
	<select id="selectPrjidTotalIncomeSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
		SUM(xx.actnum) actnum,
		SUM(xx.addnum) addnum,
		CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
		TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,

		IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
		IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,


		IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
		IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
		IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
		IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
		IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

		IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
		IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
		IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
		IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
		IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

		IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
		IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
		IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
		IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
		IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

		IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
		IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
		IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
		IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
		IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

		IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
		IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
		IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
		IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
		IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_prjid_income_sql"/>) xx
	</select>
	<sql id="dn_prjid_income_sql">
		select
		<if test="group != null and group != ''">
			${group},CONCAT(${group}) mapkey,
		</if>
		temp_id,temp_name,
		IFNULL(SUM(actnum),0) actnum,
		IFNULL(SUM(addnum),0) addnum,
		IFNULL(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),0) addrate,
		IFNULL(TRUNCATE(SUM(sum_revenue),2),0) sum_revenue,
		IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
		IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,


		IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
		IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
		IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
		IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
		IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
		IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
		IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

		IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
		IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
		IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
		IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
		IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
		IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
		IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
		IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
		IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg

		from ${tableName} a left join
		dim_prjid_function_info b on a.tdate = b.f_tdate and a.appid = b.f_appid and a.cha_id = b.f_cha_id and a.prjid = b.f_prjid
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
		<if test="temp_id != null and temp_id != '' ">
			and temp_id like concat('%',#{temp_id},'%')
		</if>
		<if test="temp_name != null and temp_name != '' ">
			and temp_name like concat('%',#{temp_name},'%')
		</if>
		<if test="match_str != null and match_str != ''">
			and ${match_str}
		</if>

		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
	</sql>

	<!-- 二级子渠道展示收入  -->
  	<select id="selectSubchaTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_subcha_total_sql"/>
	</select>
	<select id="selectSubchaTotalSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			SUM(xx.sum_revenue) sum_revenue,

			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_subcha_total_sql"/>) xx
	</select>
	<sql id="dn_subcha_total_sql">
		select
			${group},CONCAT(${group}) mapkey,
			IFNULL(SUM(actnum),0) actnum,
			IFNULL(SUM(addnum),0) addnum,
			IFNULL(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),0) addrate,
			IFNULL(SUM(sum_revenue),0) sum_revenue,
			IFNULL(CONVERT(SUM(sum_revenue) / SUM(actnum), decimal(20,2)),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg

		from ${tableName}
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="sub_cha != null and sub_cha != ''">
			and sub_cha in (${sub_cha})
		</if>
		<if test="cha_type_name != null and cha_type_name != ''">
			and cha_type_name in (${cha_type_name})
		</if>
		<if test="cha_media != null and cha_media != ''">
			and cha_media in (${cha_media})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		group by ${group}
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
	</sql>

	<!-- 聚合综合查询  -->
  	<select id="selectDnGroupCom" parameterType="java.util.Map" resultType="java.util.Map">
		select
		date,
		appid,
		cha_id,
		adsid,
		strategy,
		adpos_type,
		agent,
		sdk_adtype,
		estimate_ecpm,
		is_newuser,
		income,
		req_num,
		fill_num,
		show_num,
		click_num,
		ad_load_duration,
		CONCAT(fill_rate,'%') fill_rate,
		CONCAT(show_rate, '%') show_rate,
		CONCAT(click_rate,'%') click_rate,

		platform_ecpm,
		CONCAT(ecpm_gap,'%') ecpm_gap,
		platform_show,
		CONCAT(show_gap,'%') show_gap,
		platform_fill,
		CONCAT(fill_gap,'%') fill_gap,
		platform_req,
		CONCAT(req_gap,'%') req_gap

		from (<include refid="dn_group_com_sql"/>)
	</select>
	<select id="selectDnGroupComSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.income) income,
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(TRUNCATE(sum(xx.fill_num)/sum(xx.req_num)*100, 1),'%') fill_rate,
			CONCAT(TRUNCATE(sum(xx.show_num)/sum(xx.fill_num)*100, 1),'%') show_rate,
			CONCAT(TRUNCATE(sum(xx.click_num)/sum(xx.show_num)*100, 1),'%') click_rate,

			SUM(xx.platform_show) platform_show,
			SUM(xx.platform_fill) platform_fill,
			SUM(xx.platform_req) platform_req,
			CONCAT(TRUNCATE((SUM(xx.platform_show)-SUM(xx.show_num)) / SUM(xx.platform_show) * 100, 1),'%') show_gap,
			CONCAT(TRUNCATE((SUM(xx.platform_fill)-SUM(xx.fill_num)) / SUM(xx.platform_fill) * 100, 1),'%') fill_gap,
			CONCAT(TRUNCATE((SUM(xx.platform_req)-SUM(xx.req_num)) / SUM(xx.platform_req) * 100, 1),'%') req_gap
		from (<include refid="dn_group_com_sql"/>) xx
	</select>
	<sql id="dn_group_com_sql">
		select
			concat(tdate,'') date,
			appid,
			cha_id,
			adsid,
			strategy,
			adpos_type,
			agent,
			sdk_adtype,
			ifnull(ecpm, 0) as estimate_ecpm,
		ifnull(convert(ecpm,decimal(10,2)), 0) as ecpmForSort,
			is_newuser,
		ifnull(SUM(aa.income), 0) income,
		ifnull(SUM(aa.req_num), 0) req_num,
		ifnull(SUM(aa.fill_num), 0) fill_num,
		ifnull(SUM(aa.show_num), 0) show_num,
		ifnull(SUM(aa.click_num), 0) click_num,
		ifnull(aa.ad_load_duration, 0) ad_load_duration,
		ifnull(TRUNCATE(sum(aa.fill_num)/sum(aa.req_num)*100, 1), 0) fill_rate,
		ifnull(TRUNCATE(sum(aa.show_num)/sum(aa.fill_num)*100, 1), 0) show_rate,
		ifnull(TRUNCATE(sum(aa.click_num)/sum(aa.show_num)*100, 1), 0) click_rate,

		ifnull(platform_ecpm, 0) platform_ecpm,
		ifnull(TRUNCATE((platform_ecpm-ecpm) / ecpm * 100, 1), 0) ecpm_gap,
		ifnull(SUM(aa.platform_show), 0) platform_show,
		ifnull(TRUNCATE((SUM(aa.platform_show)-SUM(aa.show_num)) / SUM(aa.platform_show) * 100, 1), 0) show_gap,
		ifnull(SUM(aa.platform_fill), 0) platform_fill,
		ifnull(TRUNCATE((SUM(aa.platform_fill)-SUM(aa.fill_num)) / SUM(aa.platform_fill) * 100, 1), 0) fill_gap,
		ifnull(SUM(aa.platform_req), 0) platform_req,
		ifnull(TRUNCATE((SUM(aa.platform_req)-SUM(aa.req_num)) / SUM(aa.platform_req) * 100, 1), 0) req_gap

		from dnwx_bi.dn_extend_group_sum aa
		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id = #{cha_id}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid}
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent}
		</if>
		<if test="sdk_adtype != null and sdk_adtype != ''">
			and sdk_adtype = #{sdk_adtype}
		</if>
		<if test="is_newuser != null and is_newuser != ''">
			and is_newuser in (${is_newuser})
		</if>
		<if test="strategy != null and strategy != ''">
			and strategy like concat('%',#{strategy},'%')
		</if>
		<if test="income_beyond != null and income_beyond != ''">
			and income > 0
		</if>
		group by tdate,adsid
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc,strategy asc,ecpmForSort desc
			</otherwise>
		</choose>
	</sql>

	<!-- 实时瀑布流监控查询 -->
	<select id="selectDnGroupData" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_group_sql"/>
	</select>
	<select id="selectDnGroupDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.income) income,
			SUM(xx.req_num) req_num,
			SUM(xx.fill_num) fill_num,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			TRUNCATE(sum(xx.income)/sum(xx.show_num)*1000,2) ecpm,
			CONCAT(ROUND(sum(xx.fill_num)/sum(xx.req_num)*100, 2),'%') fill_rate,
			CONCAT(ROUND(sum(xx.show_num)/sum(xx.fill_num)*100, 2),'%') show_rate,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate

		from (<include refid="dn_group_sql"/>) xx
	</select>
	<sql id="dn_group_sql">
		select
			<if test="group != null and group != ''">
			${group},
			</if>
			concat(tdate,'') date,
			appid,
			<if test="ver == null or ver == ''">
			cha_id,
			</if>
			strategy,
			yy.app_name appname,
			IFNULL(yy.bus_category, '1') bus_category,
			zz.put_cha_id,
			statu,
			adpos_type,
			ecpm,
			rate,
			IFNULL(TRUNCATE(CONCAT(ecpm,'')*sum(aa.show_num)/1000,2),0) income,
			IFNULL(SUM(aa.req_num),0) req_num,
			IFNULL(SUM(aa.fill_num),0) fill_num,
			IFNULL(SUM(aa.show_num),0) show_num,
			IFNULL(SUM(aa.click_num),0) click_num,
			IFNULL(ROUND(sum(aa.fill_num)/sum(aa.req_num)*100, 2),0) fill_rate,
			IFNULL(ROUND(sum(aa.show_num)/sum(aa.fill_num)*100, 2),0) show_rate,
			IFNULL(ROUND(sum(aa.click_num)/sum(aa.show_num)*100, 2),0) click_rate
		from ${tableName} aa
		left join dnwx_adt.app_info yy on aa.appid = yy.id
		left join (select adsid sid,cha_id put_cha_id  FROM dnwx_bi.dn_extend_adsid_manage) zz on aa.adsid = zz.sid

		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="statu != null and statu != ''">
			and statu = #{statu}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type}
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid}
		</if>
		<if test="agent != null and agent != ''">
			and agent = #{agent}
		</if>
		<if test="sdktype != null and sdktype != ''">
			and SUBSTRING_INDEX(SUBSTRING_INDEX(adsid, '_', 2), '_', -1) = #{sdktype}
		</if>

		<if test="group != null and group != ''">
		 	group by ${group}
		</if>

		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY
				<foreach collection="groups" index="index" item="item" >
					CASE WHEN ${item} is null or ${item}='' THEN 1 ELSE 0 END,
				</foreach>
				${order_str}
			</when>

			<otherwise>
				order by tdate asc,adpos_type asc,ecpm desc
			</otherwise>
		</choose>

	</sql>

	<!-- 变现-广告位数据查询  -->
  	<select id="selectAdposData" parameterType="java.util.Map" resultType="java.util.Map">
		select
		mapkey,
		tdate,
		appid,cha_id,
		<if test="group != null and group != ''">
			${group},
		</if>
		dau,income,show_num,click_num,device_num,
		CONCAT(click_rate, '%') click_rate,

		CONCAT(seep_rate, '%') seep_rate,
		per_pv,
		seep_per_pv

		from (<include refid="dn_adpos_sql"/>) a
	</select>
	<select id="selectAdposDataSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.income) income,
			SUM(xx.show_num) show_num,
			SUM(xx.click_num) click_num,
			CONCAT(ROUND(sum(xx.click_num)/sum(xx.show_num)*100, 2),'%') click_rate
		from (<include refid="dn_adpos_sql"/>) xx
	</select>
	<sql id="dn_adpos_sql">
		select
			concat(appid,cha_id,adpos) mapkey,
			concat(tdate,'') tdate,
			appid,cha_id,
			<if test="group != null and group != ''">
				${group},
			</if>
			IFNULL(SUM(dau),0) dau,
			<choose>
				<when test="tableName == 'dnwx_bi.ads_dn_extend_adpos_data_daily_no_pid'">
					IFNULL(TRUNCATE(sum(income)/100,2),0) income,
				</when>
				<otherwise>
					IFNULL(TRUNCATE(sum(income),2),0) income,
				</otherwise>
			</choose>

			IFNULL(SUM(show_num),0) show_num,
			IFNULL(SUM(click_num),0) click_num,
			IFNULL(SUM(device_num),0) device_num,
			IFNULL(ROUND(sum(click_num)/sum(show_num)*100,2),0) click_rate,
			IFNULL(ROUND(sum(device_num)/dau*100,2),0) as seep_rate,
		IFNULL(TRUNCATE(sum(show_num)/dau, 1),0) as per_pv,
		IFNULL(TRUNCATE(sum(show_num)/sum(device_num), 1),0) as seep_per_pv
		from ${tableName}
		where tdate BETWEEN #{start_date} AND #{end_date}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="adpos != null and adpos != ''">
			and adpos = #{adpos}
		</if>
		<if test="adpos_type != null and adpos_type != ''">
			and adpos_type = #{adpos_type}
		</if>
		<if test="adsid != null and adsid != ''">
			and adsid = #{adsid}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		<choose>
			<when test="group != null and group != ''">
				group by tdate,appid,cha_id, ${group}
			</when>
			<otherwise>
				group by tdate,appid,cha_id
			</otherwise>
		</choose>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate asc,adpos_type asc,income desc
			</otherwise>
		</choose>

	</sql>

	<!-- 汇总数据-用户群  -->
  	<select id="selectAdtypeTotalGroup" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="dn_group_adtype_sql"/>
	</select>
	<select id="selectAdtypeTotalGroupSum" parameterType="java.util.Map" resultType="java.util.Map">
		select
			SUM(xx.actnum) actnum,
			SUM(xx.addnum) addnum,
			CONCAT(TRUNCATE(SUM(xx.addnum) / SUM(xx.actnum)*100, 1),'%') addrate,
			TRUNCATE(SUM(xx.sum_revenue),2) sum_revenue,

			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.actnum), 2),0) dau_arpu,
			IFNULL(TRUNCATE(SUM(xx.sum_revenue) / SUM(xx.addnum), 2),0) add_arpu,


			IFNULL(TRUNCATE(SUM(xx.pv_splash) / SUM(xx.actnum), 1),0) avg_pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque) / SUM(xx.actnum), 1),0) avg_pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner) / SUM(xx.actnum), 1),0) avg_pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video) / SUM(xx.actnum), 1),0) avg_pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg) / SUM(xx.actnum), 1),0) avg_pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.pv_splash)*1000, 1),0) ecpm_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.pv_plaque)*1000, 1),0) ecpm_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.pv_banner)*1000, 1),0) ecpm_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.pv_video)*1000, 1),0) ecpm_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.pv_msg)*1000, 1),0) ecpm_msg,

			IFNULL(TRUNCATE(SUM(xx.pv_splash), 0),0) pv_splash,
			IFNULL(TRUNCATE(SUM(xx.pv_plaque), 0),0) pv_plaque,
			IFNULL(TRUNCATE(SUM(xx.pv_banner), 0),0) pv_banner,
			IFNULL(TRUNCATE(SUM(xx.pv_video), 0),0) pv_video,
			IFNULL(TRUNCATE(SUM(xx.pv_msg), 0),0) pv_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash), 2),0) revenue_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque), 2),0) revenue_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner), 2),0) revenue_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video), 2),0) revenue_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg), 2),0) revenue_msg,

			IFNULL(TRUNCATE(SUM(xx.revenue_splash) / SUM(xx.actnum), 2),0) arpu_splash,
			IFNULL(TRUNCATE(SUM(xx.revenue_plaque) / SUM(xx.actnum), 2),0) arpu_plaque,
			IFNULL(TRUNCATE(SUM(xx.revenue_banner) / SUM(xx.actnum), 2),0) arpu_banner,
			IFNULL(TRUNCATE(SUM(xx.revenue_video) / SUM(xx.actnum), 2),0) arpu_video,
			IFNULL(TRUNCATE(SUM(xx.revenue_msg) / SUM(xx.actnum), 2),0) arpu_msg

		from (<include refid="dn_group_adtype_sql"/>) xx
	</select>
	<sql id="dn_group_adtype_sql">
		select
		<if test="group != null and group != ''">
			${group}, CONCAT(${group}) mapkey,
		</if>
		IFNULL(SUM(actnum),0) actnum,
		IFNULL(SUM(addnum),0) addnum,
		IFNULL(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1),0) addrate,
		IFNULL(TRUNCATE(SUM(sum_revenue),2),0) sum_revenue,
		IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(actnum), 2),0) dau_arpu,
		IFNULL(TRUNCATE(SUM(sum_revenue) / SUM(addnum), 2),0) add_arpu,


		IFNULL(TRUNCATE(SUM(pv_splash) / SUM(actnum), 1),0) avg_pv_splash,
		IFNULL(TRUNCATE(SUM(pv_plaque) / SUM(actnum), 1),0) avg_pv_plaque,
		IFNULL(TRUNCATE(SUM(pv_banner) / SUM(actnum), 1),0) avg_pv_banner,
		IFNULL(TRUNCATE(SUM(pv_video) / SUM(actnum), 1),0) avg_pv_video,
		IFNULL(TRUNCATE(SUM(pv_msg) / SUM(actnum), 1),0) avg_pv_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(pv_splash)*1000, 1),0) ecpm_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(pv_plaque)*1000, 1),0) ecpm_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(pv_banner)*1000, 1),0) ecpm_banner,
		IFNULL(TRUNCATE(SUM(revenue_video) / SUM(pv_video)*1000, 1),0) ecpm_video,
		IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(pv_msg)*1000, 1),0) ecpm_msg,

		IFNULL(TRUNCATE(SUM(pv_splash), 0),0) pv_splash,
		IFNULL(TRUNCATE(SUM(pv_plaque), 0),0) pv_plaque,
		IFNULL(TRUNCATE(SUM(pv_banner), 0),0) pv_banner,
		IFNULL(TRUNCATE(SUM(pv_video), 0),0) pv_video,
		IFNULL(TRUNCATE(SUM(pv_msg), 0),0) pv_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash), 2),0) revenue_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque), 2),0) revenue_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner), 2),0) revenue_banner,
		IFNULL(TRUNCATE(SUM(revenue_video), 2),0) revenue_video,
		IFNULL(TRUNCATE(SUM(revenue_msg), 2),0) revenue_msg,

		IFNULL(TRUNCATE(SUM(revenue_splash) / SUM(actnum), 2),0) arpu_splash,
		IFNULL(TRUNCATE(SUM(revenue_plaque) / SUM(actnum), 2),0) arpu_plaque,
		IFNULL(TRUNCATE(SUM(revenue_banner) / SUM(actnum), 2),0) arpu_banner,
		IFNULL(TRUNCATE(SUM(revenue_video) / SUM(actnum), 2),0) arpu_video,
		IFNULL(TRUNCATE(SUM(revenue_msg) / SUM(actnum), 2),0) arpu_msg

		from ${tableName}
		where tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid = #{prjid}
		</if>
		<if test="user_group != null and user_group != ''">
			and user_group = #{user_group}
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		<if test="group != null and group != ''">
			 group by ${group}
		</if>
		<choose>
			<when test="order_str != null and order_str != ''">
				order by ${order_str}
			</when>
			<otherwise>
				order by tdate desc,actnum desc
			</otherwise>
		</choose>
	</sql>

	<!-- 实时数据监控 -->
	<insert id="insertDnGroupDataMonitor" parameterType="java.util.Map" >
		INSERT INTO dnwx_bi.dn_extend_group_monitor(tdate,appid,cha_id,prjid,income,plaque_pv,plaque_income,video_pv,video_income,msg_pv,msg_income,actnum,addnum)

		SELECT aa.tdate,aa.appid,aa.cha_id,aa.prjid,TRUNCATE(SUM(CONCAT(aa.income,'')),2) income,
			SUM(CASE WHEN aa.adpos_type = 'plaque' THEN aa.show_num ELSE 0 END) plaque_pv,
			TRUNCATE(SUM(CASE WHEN aa.adpos_type = 'plaque' THEN CONCAT(aa.income,'') ELSE 0 END),2) plaque_income,
			SUM(CASE WHEN aa.adpos_type = 'video' THEN aa.show_num ELSE 0 END) video_pv,
			TRUNCATE(SUM(CASE WHEN aa.adpos_type = 'video' THEN CONCAT(aa.income,'') ELSE 0 END),2) video_income,
			SUM(CASE WHEN aa.adpos_type = 'msg' THEN aa.show_num ELSE 0 END) msg_pv,
			TRUNCATE(SUM(CASE WHEN aa.adpos_type = 'msg' THEN CONCAT(aa.income,'') ELSE 0 END),2) msg_income,
			bb.act_users,bb.new_users

		from (
			select
				tdate,prjid,cha_id,adsid,adpos_type,
				appid,
				ecpm,
				TRUNCATE(ecpm*sum(aa.show_num)/1000,2) income,
				SUM(aa.req_num) req_num,
				SUM(aa.fill_num) fill_num,
				SUM(aa.show_num) show_num,
				SUM(aa.click_num) click_num
			from dnwx_bi.ads_dn_extend_group_data_two_daily aa
			where tdate BETWEEN #{tdate} AND #{tdate}
			group by tdate,prjid,cha_id,adsid
		) aa left join dnwx_bi.ads_dim_users_info_4d_hourly bb
		ON aa.tdate=bb.tdate and aa.prjid=bb.pid and aa.cha_id=bb.download_channel

		WHERE aa.adpos_type in ('plaque','video','msg') AND aa.income is not null
		GROUP BY aa.prjid,aa.cha_id
	</insert>

	<select id="selectExtendGroupMonitor" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.DnExtendGroupMonitorVo">
		<include refid="dn_group_monitor_sql"/>
	</select>
	<sql id="dn_group_monitor_sql">
		SELECT tdate,appid,prjid,cha_id,SUM(actnum) actnum,SUM(addnum) addnum,convert(SUM(income),decimal(10,2)) income,yy.app_name appname,
			IFNULL(TRUNCATE(SUM(addnum) / SUM(actnum)*100, 1), 0) addrate,
			IFNULL(TRUNCATE(SUM(income) / SUM(actnum), 1), 0) dau_arpu,
			IFNULL(TRUNCATE(SUM(plaque_pv) / SUM(actnum), 1), 0) plaque_per_pv,
			IFNULL(TRUNCATE(SUM(plaque_income) / SUM(plaque_pv)*1000, 1), 0) plaque_ecpm,
			IFNULL(TRUNCATE(SUM(video_pv) / SUM(actnum), 1), 0) video_per_pv,
			IFNULL(TRUNCATE(SUM(video_income) / SUM(video_pv)*1000, 1), 0) video_ecpm,
			IFNULL(TRUNCATE(SUM(msg_pv) / SUM(actnum), 1), 0) msg_per_pv,
			IFNULL(TRUNCATE(SUM(msg_income) / SUM(msg_pv)*1000, 1), 0) msg_ecpm

		FROM dnwx_bi.dn_extend_group_monitor xx left join  dnwx_adt.app_info yy
		ON xx.appid = yy.id

		WHERE tdate BETWEEN #{sdate} AND #{edate}
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="prjid != null and prjid != ''">
			and prjid in (${prjid})
		</if>
		<if test="cha_id != null and cha_id != ''">
			and cha_id in (${cha_id})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>
		and actnum >= 50
		GROUP BY ${groupStr}


		<choose>
			<when test="order_str != null and order_str != ''">
				ORDER BY
				<foreach collection="groups" index="index" item="item" >
					CASE WHEN ${item} is null or ${item}='' THEN 1 ELSE 0 END,
				</foreach>
				${order_str}
			</when>

			<otherwise>
				ORDER BY tdate desc,actnum desc
			</otherwise>
		</choose>
	</sql>



	<select id="selectCurrentEventInfo" parameterType="java.util.Map" resultType="java.util.Map">

		select t_date tdate,appid,loginid,openid,android_id,idfa,ip,event_id,event_data,opencode,usercode,uuid

		from dnwx_bi.ads_wechat_event_realtime where t_date BETWEEN '${stime}' and '${etime}'
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="loginid != null and loginid != ''">
			and loginid = #{loginid}
		</if>
		<if test="openid != null and openid != ''">
			and openid = #{openid}
		</if>
		<if test="android_id != null and android_id != ''">
			and android_id = #{android_id}
		</if>
		<if test="idfa != null and idfa != ''">
			and idfa = #{idfa}
		</if>
		<if test="ip != null and ip != ''">
			and ip = #{ip}
		</if>
		<if test="event_id != null and event_id != ''">
			and event_id in (${event_id})
		</if>
		<if test="apps != null and apps != ''">
			and appid in (${apps})
		</if>

		<choose>
			<when test="order != null and order != ''">
				order by ${order}
			</when>
			<otherwise>
				order by t_date asc
			</otherwise>
		</choose>
	</select>
	<select id="getChinaMonetizationReportByDay" resultType="com.wbgame.pojo.adv2.reportEntity.ReportChina">
		SELECT `date`, dnappid, cha_id, open_type, country, SUM(revenue) AS revenue, SUM(pv) AS pv, SUM(pv_out) AS pv_out, SUM(revenue_out) AS revenue_out
		FROM ( select *,
					  case
						  when `out` = 1 then revenue
						  else 0
						  end AS revenue_out,
					  case
						  when `out` = 1 then pv
						  else 0
						  end AS pv_out
		              from
				 dnwx_bi.dn_cha_cash_total
				 ) t
		WHERE `date` = #{day}
		GROUP BY `date`, dnappid, cha_id, open_type, country
	</select>
    <select id="selectShortcutReport" resultType="com.wbgame.pojo.attribution.ShortcutReport">
		<include refid="selectShortcutReportSql"/>
	</select>
	<select id="selectShortcutReportSum" resultType="com.wbgame.pojo.attribution.ShortcutReport">
		select
		IFNULL(SUM(mau), 0) mau
		,IFNULL(SUM(dau), 0) dau
		,IFNULL(SUM(click_cnt), 0) click_cnt
		,IFNULL(SUM(valid_click_cnt), 0) valid_click_cnt
		,IFNULL(SUM(click_users), 0) click_users
		,IFNULL(SUM(active_users), 0) active_users
		,IFNULL(SUM(retention_1), 0) retention_1
		,IFNULL(SUM(retention_2), 0) retention_2
		,IFNULL(SUM(retention_3), 0) retention_3
		,IFNULL(SUM(retention_4), 0) retention_4
		,IFNULL(SUM(retention_5), 0) retention_5
		,IFNULL(SUM(retention_6), 0) retention_6
		,IFNULL(SUM(retention_7), 0) retention_7
		,IFNULL(SUM(retention_14), 0) retention_14
		,IFNULL(SUM(retention_30), 0) retention_30
		,IFNULL(SUM(retention_45), 0) retention_45
		,IFNULL(SUM(retention_90), 0) retention_90
		,IFNULL(TRUNCATE(SUM(retention_1)/sum(active_users), 2), 0) retention_ratio_1
		,IFNULL(TRUNCATE(SUM(retention_2)/sum(active_users), 2), 0) retention_ratio_2
		,IFNULL(TRUNCATE(SUM(retention_3)/sum(active_users), 2), 0) retention_ratio_3
		,IFNULL(TRUNCATE(SUM(retention_4)/sum(active_users), 2), 0) retention_ratio_4
		,IFNULL(TRUNCATE(SUM(retention_5)/sum(active_users), 2), 0) retention_ratio_5
		,IFNULL(TRUNCATE(SUM(retention_6)/sum(active_users), 2), 0) retention_ratio_6
		,IFNULL(TRUNCATE(SUM(retention_7)/sum(active_users), 2), 0) retention_ratio_7
		,IFNULL(TRUNCATE(SUM(retention_14)/sum(active_users), 2), 0) retention_ratio_14
		,IFNULL(TRUNCATE(SUM(retention_30)/sum(active_users), 2), 0) retention_ratio_30
		,IFNULL(TRUNCATE(SUM(retention_45)/sum(active_users), 2), 0) retention_ratio_45
		,IFNULL(TRUNCATE(SUM(retention_90)/sum(active_users), 2), 0) retention_ratio_90
		    from (
			<include refid="selectShortcutReportSql"/>
		) t
	</select>
	<select id="selectSelfSubmitRecord" resultType="com.wbgame.pojo.attribution.SelfSubmitRecord">
		select platform, appid, event_type eventType, content, ret, android_id androidId, a.create_time createTime, b.app_name appName
		from dnwx_adt.self_submit_records a
		left join app_info b on a.appid = b.id
		where a.create_time between date(#{startDate}) and date(#{endDate})
		<if test="platform != null and platform != ''">
			and platform in (${platform})
		</if>
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="eventType != null and eventType != ''">
			and event_type in (${eventType})
		</if>
		<if test="androidId != null and androidId != ''">
			and android_id = #{androidId}
		</if>
		<if test="appCategory != null and appCategory != ''">
			and app_category in (${appCategory})
		</if>
		order by a.create_time desc
	</select>

	<sql id="selectShortcutReportSql">
		select
		    <if test="group != null and group != ''">
				${group},
			</if>
		     b.app_name
		,IFNULL(SUM(mau), 0) mau
		,IFNULL(SUM(dau), 0) dau
		,IFNULL(SUM(click_cnt), 0) click_cnt
		,IFNULL(SUM(valid_click_cnt), 0) valid_click_cnt
		,IFNULL(SUM(click_users), 0) click_users
		,IFNULL(SUM(active_users), 0) active_users
		,IFNULL(SUM(retention_1), 0) retention_1
		,IFNULL(SUM(retention_2), 0) retention_2
		,IFNULL(SUM(retention_3), 0) retention_3
		,IFNULL(SUM(retention_4), 0) retention_4
		,IFNULL(SUM(retention_5), 0) retention_5
		,IFNULL(SUM(retention_6), 0) retention_6
		,IFNULL(SUM(retention_7), 0) retention_7
		,IFNULL(SUM(retention_14), 0) retention_14
		,IFNULL(SUM(retention_30), 0) retention_30
		,IFNULL(SUM(retention_45), 0) retention_45
		,IFNULL(SUM(retention_90), 0) retention_90
		,IFNULL(TRUNCATE(SUM(retention_1)/sum(active_users), 2), 0) retention_ratio_1
		,IFNULL(TRUNCATE(SUM(retention_2)/sum(active_users), 2), 0) retention_ratio_2
		,IFNULL(TRUNCATE(SUM(retention_3)/sum(active_users), 2), 0) retention_ratio_3
		,IFNULL(TRUNCATE(SUM(retention_4)/sum(active_users), 2), 0) retention_ratio_4
		,IFNULL(TRUNCATE(SUM(retention_5)/sum(active_users), 2), 0) retention_ratio_5
		,IFNULL(TRUNCATE(SUM(retention_6)/sum(active_users), 2), 0) retention_ratio_6
		,IFNULL(TRUNCATE(SUM(retention_7)/sum(active_users), 2), 0) retention_ratio_7
		,IFNULL(TRUNCATE(SUM(retention_14)/sum(active_users), 2), 0) retention_ratio_14
		,IFNULL(TRUNCATE(SUM(retention_30)/sum(active_users), 2), 0) retention_ratio_30
		,IFNULL(TRUNCATE(SUM(retention_45)/sum(active_users), 2), 0) retention_ratio_45
		,IFNULL(TRUNCATE(SUM(retention_90)/sum(active_users), 2), 0) retention_ratio_90
		from ads_self_attribution_platform_backend_data_hourly a
		left join app_info b
		on a.appid = b.id
		where tdate between #{startDate} and #{endDate}
		<if test="special_name != null and special_name != ''">
			and special_name in (${special_name})
		</if>
		<if test="appid != null and appid != ''">
			and appid in (${appid})
		</if>
		<if test="appCategory != null and appCategory != ''">
			and app_category in (${appCategory})
		</if>
		<if test="group != null and group != ''">
			group by ${group}
		</if>
		<if test="order != null and order != ''">
			order by ${order}
		</if>
	</sql>


	<insert id="insertAdsDnShowGapAutoctr" parameterType="java.util.Map" >
		REPLACE INTO dnwx_bi.dn_show_gap_autoctr(tdate,appid,channel,sdk_adtype,click_filter_rate,s_click_rate)

		SELECT t.*,IFNULL(y.s_click_rate,0) FROM
			(
				select
					 '${edate}' tdate,appid,channel,sdk_adtype,
					 IFNULL(TRUNCATE((sum(s_click_rate) - sum(click_rate))/NULLIF(sum(s_click_rate),0)*100,2),0.00) click_filter_rate

				 from
					 (
						 select tdate,appid,channel,sdk_adtype,
								IFNULL(TRUNCATE(sum(click_num)/NULLIF(sum(selfshow_num),0)*100,2),0) s_click_rate,
								IFNULL(TRUNCATE(sum(click)/NULLIF(sum(pv),0)*100,2),0) click_rate

						 from dnwx_bi.ads_dn_show_gap_daily
						 where tdate BETWEEN '${sdate}' AND '${edate}' and `out`=0
						 group by tdate,appid,channel,sdk_adtype
					 ) c
				 group by appid,channel,sdk_adtype
			 ) t

		LEFT JOIN
			(
				SELECT
					'${edate}' tdate,appid,channel,sdk_adtype,
					IFNULL(TRUNCATE(AVG(s_click_rate),2),0) AS s_click_rate
				FROM (
						-- 计算前七天的s_click_rate
						SELECT
							appid,channel,sdk_adtype,
							TRUNCATE(SUM(click_num) / NULLIF(SUM(selfshow_num), 0) * 100, 2) AS s_click_rate
						FROM dnwx_bi.ads_dn_show_gap_daily
						WHERE
							tdate BETWEEN '${sdate}' AND '${edate}' and `out`=0
						GROUP BY appid,channel,sdk_adtype

						UNION ALL

						-- 计算前一天的s_click_rate
						SELECT
							appid,channel,sdk_adtype,
							TRUNCATE(SUM(click_num) / NULLIF(SUM(selfshow_num), 0) * 100, 2) AS s_click_rate
						FROM dnwx_bi.ads_dn_show_gap_daily
						WHERE tdate = '${yesterday}' and `out`=0
						GROUP BY appid,channel,sdk_adtype

					) AS combined_data
				GROUP BY appid,channel,sdk_adtype
			) y
		ON t.tdate=y.tdate and t.appid=y.appid and t.channel=y.channel and t.sdk_adtype=y.sdk_adtype

	</insert>
	<insert id="insertAdsDnShowGapAutoctrAsOut" parameterType="java.util.Map" >
		REPLACE INTO dnwx_bi.dn_show_gap_autoctr(tdate,appid,channel,sdk_adtype,click_filter_rate,s_click_rate)

		SELECT t.*,IFNULL(y.s_click_rate,0) FROM
			(
				select
					'${edate}' tdate,appid,channel,sdk_adtype,
					IFNULL(TRUNCATE((sum(s_click_rate) - sum(click_rate))/NULLIF(sum(s_click_rate),0)*100,2),0.00) click_filter_rate

				from
					(
						select tdate,appid,channel,'out' sdk_adtype,
							   IFNULL(TRUNCATE(sum(click_num)/NULLIF(sum(selfshow_num),0)*100,2),0) s_click_rate,
							   IFNULL(TRUNCATE(sum(click)/NULLIF(sum(pv),0)*100,2),0) click_rate

						from dnwx_bi.ads_dn_show_gap_daily
						where tdate BETWEEN '${sdate}' AND '${edate}' and `out`=1
						group by tdate,appid,channel
					) c
				group by appid,channel,sdk_adtype
			) t

		LEFT JOIN
			(
				SELECT
					'${edate}' tdate,appid,channel,sdk_adtype,
					IFNULL(TRUNCATE(AVG(s_click_rate),2),0) AS s_click_rate
				FROM (
						 -- 计算前七天的s_click_rate
						 SELECT
							 appid,channel,'out' sdk_adtype,
							 TRUNCATE(SUM(click_num) / NULLIF(SUM(selfshow_num), 0) * 100, 2) AS s_click_rate
						 FROM dnwx_bi.ads_dn_show_gap_daily
						 WHERE
							 tdate BETWEEN '${sdate}' AND '${edate}' and `out`=1
						 GROUP BY appid,channel

						 UNION ALL

						 -- 计算前一天的s_click_rate
						 SELECT
							 appid,channel,'out' sdk_adtype,
							 TRUNCATE(SUM(click_num) / NULLIF(SUM(selfshow_num), 0) * 100, 2) AS s_click_rate
						 FROM dnwx_bi.ads_dn_show_gap_daily
						 WHERE tdate = '${yesterday}' and `out`=1
						 GROUP BY appid,channel

					 ) AS combined_data
				GROUP BY appid,channel,sdk_adtype
			) y
		ON t.tdate=y.tdate and t.appid=y.appid and t.channel=y.channel and t.sdk_adtype=y.sdk_adtype

	</insert>


	<select id="selectDnRedLineClickCtr" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.ExtendAdposVo">
		SELECT '${tdate}' `note`,appid,cha_id,prjid,user_group,adpos_type,sdktype,
		       truncate(avg(real_ctr),2) real_ctr
		FROM
		(
			SELECT xx.*,sdktype as config,IFNULL(yy.gap,0) gap,truncate(click_rate*(1-IFNULL(yy.gap,0)),2) real_ctr FROM
				(
					SELECT
						 appid,cha_id,prjid,user_group,adpos_type,adsid,
						 SUBSTRING_INDEX(SUBSTRING_INDEX(adsid, '_', 2), '_', -1) sdktype,
						 IFNULL(ROUND((click_num)/(show_num)*100, 2),0) click_rate,
						 IFNULL(show_num,0) show_num,
						 IFNULL(click_num,0) click_num
					FROM ads_dn_extend_group_data_two_daily
					where tdate=#{tdate}
						and cha_id in ('vivo','vivo2','vivoml','vivoml2')

						and adpos_type='plaque'
						and click_num > 0
						and SUBSTRING_INDEX(SUBSTRING_INDEX(adsid, '_', 2), '_', -1) in ('yuans','msg','plaque')
						and (user_group not like '%_A' and user_group != 'all')
				) xx
			LEFT JOIN
				(
					SELECT ad.*,
						IFNULL(TRUNCATE((s_click_rate - p_click_rate)/(s_click_rate),4),0) gap
					FROM (
						select tdate,adsid,sdk_adtype,
							IFNULL(TRUNCATE(click_num/selfshow_num*100,2),0) s_click_rate,
							IFNULL(TRUNCATE(click/pv*100,2),0) p_click_rate
						from ads_dn_show_gap_daily
						where tdate=DATE_SUB(#{tdate},INTERVAL 1 DAY) and click_num > 0
					) ad
				) yy
			ON xx.adsid=yy.adsid
		) zz
		GROUP BY appid,cha_id,prjid,user_group,adpos_type,sdktype
		HAVING real_ctr > ${ctr_val}
		ORDER BY prjid

	</select>
	<select id="selectDnExtendAdposClickAndDau" parameterType="java.util.Map" resultType="com.wbgame.pojo.adv2.ExtendAdposVo">
		SELECT
			CONCAT(tdate,appid,cha_id,prjid,sdktype) `config`,
			x.* FROM
		(
			SELECT tdate,appid,cha_id,prjid,adpos,
				   IF(adpos='time_ad997','yuans',IF(adpos='time_ad998','plaque',IF(adpos='time_ad999','msg','') )) sdktype,
				   dau,
				   IFNULL(SUM(show_num),0) show_num,
				   IFNULL(SUM(click_num),0) click_num,
				   IFNULL(ROUND(SUM(click_num)/SUM(show_num)*100, 2),0) ctr
			FROM `ads_dn_extend_adpos_data_click_and_dau_hourly`
			WHERE tdate=#{tdate}
					and adpos_type='plaque'
					and adpos like 'time_ad99%'

			GROUP BY tdate,appid,cha_id,prjid,adpos
			ORDER BY prjid,adpos
		) x
	</select>



	<select id="selectSimuClickData" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT CONCAT(appid,'#',channel) mapkey FROM dnwx_bi.ads_simu_click_daily
		WHERE tdate=DATE_SUB(#{tdate},INTERVAL 1 DAY)
		GROUP BY appid,channel
		HAVING SUM(simuclick1)=0 and SUM(simuclick2)=0 and SUM(simuclick3)=0

	</select>
	<select id="selectReportSpendData" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT mapkey FROM
		(
			select CONCAT(app,'#',channel1) mapkey,app,channel1,IFNULL(SUM(rebateSpend),0) spend
			 from dnwx_adt.dn_report_spend_china
			 where `day`=DATE_SUB(#{tdate},INTERVAL 1 DAY)
			 group by app,channel1
			 HAVING spend = 0
			 ORDER BY spend
		) x

	</select>
	<select id="selectSdkLocalverData" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT CONCAT(appid,'#',cha_id,'#',prjid) mapkey FROM dnwx_bi.dn_extend_sdk_relation
		WHERE tdate=DATE_SUB(#{tdate},INTERVAL 1 DAY)
		and sdk_name='大版本的版本号'
		and cast(replace(local_ver,'.','') as SIGNED) &lt; 1830
	</select>

</mapper>