package com.wbgame.service;

import java.util.List;
import java.util.Map;

import org.springframework.cache.annotation.Cacheable;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.wbgame.pojo.BonusInfoVo;
import com.wbgame.pojo.custom.CustomStatsVo;

public interface BonusService {
	
	// 用户数据入库
	int insertWxBonusUserInfoList(List<Map<String, Object>> list);
	// 提现记录入库
	int insertWxMoneyPutcashList(List<Map<String, Object>> list);
	
 	BonusInfoVo selectWxBonusUserInfo(String wbappid, String openid);
 	
}
