package com.wbgame.service.product;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.product.HwProductManage;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 华为-商品业务层
 * @Date 2024/10/23 11:17
 */
public interface HwProductInfoService {

    /**
     * 华为商品导入数据查询
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    Result<List<HwProductManage>> queryList(HwProductManage dto);

    /**
     * 批量新增华为商品数据
     *
     * @param productList 新增数据
     * @return 新增结果
     */
    Result<String> batchImport(List<HwProductManage> productList);

    /**
     * 校验导入的excel数据
     *
     * @param excelList 导入的数据
     * @return 校验结果
     */
    Result<List<HwProductManage>> checkExcelDataFormat(List<ArrayList<String>> excelList);

    /**
     * 新增商品管理数据
     *
     * @param dto 新增的数据
     * @return 新增结果
     */
    Result<Integer> insertProduct(HwProductManage dto);

    /**
     * 更新商品管理数据
     *
     * @param dto 更新的数据
     * @return 更新结果
     */
    Result<Integer> updateProduct(HwProductManage dto);
}
