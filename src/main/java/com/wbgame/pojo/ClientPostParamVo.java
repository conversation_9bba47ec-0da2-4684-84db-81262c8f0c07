package com.wbgame.pojo;

import java.io.Serializable;

/**
 * 客户端post参数日志天表
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class ClientPostParamVo implements Serializable {
	
	private String id;
	private String lsn;
	private String mmid;
	private String imei;
	private String imsi;// varchar2(200),
	private String productid;// number(10),
	private String projectid;// number(10),
	private String smscenter;// varchar2(200),
	private String version;// number(10),
	private String release_verno;// varchar2(200),
	private String screnres;// varchar2(200),
	private String createtime;// date,
	private String paramstring;// varchar2(10)
	private String opencount;
	private String cha_id;

	private String beginTime;
	private String endTime;
	
	private String oaid;
	private String macaddr;
	private String cityid;
	private String net;
	private String gametimes;
	private String mobilemodel;
	private String wifissid;
	private String cname;
	private String appname;

	private String bit0;//周期更新开关
	private String bit1;//每天流量更新开关
	private String bit2;//计费开关
	private String bit3;//前置计费开关
	private String bit4;//进应用每次更新开关
	private String bit5;//提示文字全部显示开关
	
	private String company;//公司

	private String order;//排序
	
	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getBit0() {
		return bit0;
	}

	public void setBit0(String bit0) {
		this.bit0 = bit0;
	}

	public String getBit1() {
		return bit1;
	}

	public void setBit1(String bit1) {
		this.bit1 = bit1;
	}

	public String getBit2() {
		return bit2;
	}

	public void setBit2(String bit2) {
		this.bit2 = bit2;
	}

	public String getBit3() {
		return bit3;
	}

	public void setBit3(String bit3) {
		this.bit3 = bit3;
	}

	public String getBit4() {
		return bit4;
	}

	public void setBit4(String bit4) {
		this.bit4 = bit4;
	}

	public String getBit5() {
		return bit5;
	}

	public void setBit5(String bit5) {
		this.bit5 = bit5;
	}

	public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLsn() {
		return lsn;
	}

	public void setLsn(String lsn) {
		this.lsn = lsn;
	}

	public String getImsi() {
		return imsi;
	}

	public void setImsi(String imsi) {
		this.imsi = imsi;
	}

	public String getProductid() {
		return productid;
	}

	public void setProductid(String productid) {
		this.productid = productid;
	}

	public String getProjectid() {
		return projectid;
	}

	public void setProjectid(String projectid) {
		this.projectid = projectid;
	}

	public String getSmscenter() {
		return smscenter;
	}

	public void setSmscenter(String smscenter) {
		this.smscenter = smscenter;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getRelease_verno() {
		return release_verno;
	}

	public void setRelease_verno(String release_verno) {
		this.release_verno = release_verno;
	}

	public String getScrenres() {
		return screnres;
	}

	public void setScrenres(String screnres) {
		this.screnres = screnres;
	}

	public String getCreatetime() {
		return createtime;
	}

	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}

	public String getParamstring() {
		return paramstring;
	}

	public void setParamstring(String paramstring) {
		if(paramstring != null && !"".equals(paramstring))
		{
			char [] flagArr = paramstring.toCharArray();
			if('1' == flagArr[0])
			{
				this.setBit5("1");
			}
			else
			{
				this.setBit5("0");
			}
			if('1' == flagArr[1])
			{
				this.setBit4("1");
			}
			else
			{
				this.setBit4("0");
			}
			if('1' == flagArr[2])
			{
				this.setBit3("1");
			}
			else
			{
				this.setBit3("0");
			}
			if('1' == flagArr[3])
			{
				this.setBit2("1");
			}
			else
			{
				this.setBit2("0");
			}
			if('1' == flagArr[4])
			{
				this.setBit1("1");
			}
			else
			{
				this.setBit1("0");
			}
			if('1' == flagArr[5])
			{
				this.setBit0("1");
			}
			else
			{
				this.setBit0("0");
			}
		}
		this.paramstring = paramstring;
	}

	public String getMmid() {
		return mmid;
	}

	public void setMmid(String mmid) {
		this.mmid = mmid;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public String getOaid() {
		return oaid;
	}

	public void setOaid(String oaid) {
		this.oaid = oaid;
	}

	public String getMacaddr() {
		return macaddr;
	}

	public void setMacaddr(String macaddr) {
		this.macaddr = macaddr;
	}

	public String getCityid() {
		return cityid;
	}

	public void setCityid(String cityid) {
		this.cityid = cityid;
	}

	public String getNet() {
		return net;
	}

	public void setNet(String net) {
		this.net = net;
	}

	public String getGametimes() {
		return gametimes;
	}

	public void setGametimes(String gametimes) {
		this.gametimes = gametimes;
	}

	public String getMobilemodel() {
		return mobilemodel;
	}

	public void setMobilemodel(String mobilemodel) {
		this.mobilemodel = mobilemodel;
	}

	public String getOpencount() {
		return opencount;
	}

	public void setOpencount(String opencount) {
		this.opencount = opencount;
	}

	public String getCha_id() {
		return cha_id;
	}

	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}

	public String getWifissid() {
		return wifissid;
	}

	public void setWifissid(String wifissid) {
		this.wifissid = wifissid;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}

	public String getAppname() {
		return appname;
	}

	public void setAppname(String appname) {
		this.appname = appname;
	}
	
}
