package com.wbgame.controller.advert.oversea;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.advert.oversea.HuaweiCashReport;
import com.wbgame.pojo.advert.oversea.PlacementReport;
import com.wbgame.pojo.budgetWarning.BudgetStatisticField;
import com.wbgame.service.advert.oversea.PlacementIncomeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 **/
@CrossOrigin
@RestController
@RequestMapping("/oversea/income")
@Api(tags = "海外数据同步接口")
@ApiSupport(author = "shenl")
@Slf4j
public class PlacementIncomeReportController {

    @Resource
    private PlacementIncomeService placementIncomeService;

    @RequestMapping(value = "/placement",method = {RequestMethod.POST })
    @ApiOperation(value = "插入广告位级别数据", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String placementIncomeReport(@RequestBody List<PlacementReport> list) {
        log.info("insert placementIncomeReport:{}",list.size());
        placementIncomeService.placementIncomeReport(list);
        return ReturnJson.success();
    }

    @RequestMapping(value = "/huawei/account/cash",method = {RequestMethod.POST })
    @ApiOperation(value = "插入华为账户消耗数据", httpMethod = "POST")
    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    public String huaweiAccountCashReport(@RequestBody List<HuaweiCashReport> list) {
        log.info("insert huaweiAccountCashReport:{}",list.size());
        placementIncomeService.huaweiAccountCashReport(list);
        return ReturnJson.success();
    }
}
