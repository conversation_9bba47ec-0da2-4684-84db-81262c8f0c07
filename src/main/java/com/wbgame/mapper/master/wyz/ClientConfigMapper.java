package com.wbgame.mapper.master.wyz;

import com.wbgame.pojo.wyz.ClientConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ClientConfigMapper {
    int insertClientConfig(@Param("config") ClientConfigVo clientConfigVo);

    int deleteClientConfig(@Param("id") int id);

    List<ClientConfigVo> selectClientConfig(@Param("config") ClientConfigVo clientConfigVo);

    int updateClientConfig(@Param("config") ClientConfigVo clientConfigVo);
}
