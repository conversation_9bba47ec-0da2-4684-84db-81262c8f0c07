package com.wbgame.servlet.qihoo;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Calendar;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wbgame.utils.BlankUtils;

@WebServlet(name="qihooApiClick",urlPatterns="/qihooApiClick")
public class QihooApiClick extends HttpServlet {

	private static Logger log = LoggerFactory.getLogger(QihooApiClick.class);
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * Constructor of the object.
	 */
	public QihooApiClick() {
		super();
	}

	/**
	 * Destruction of the servlet. <br>
	 */
	public void destroy() {
		super.destroy(); // Just puts "destroy" string in log
		// Put your code here
	}

	/**
	 * The doGet method of the servlet. <br>
	 *
	 * This method is called when a form has its tag value method equals to get.
	 * 
	 * @param request the request send by the client to the server
	 * @param response the response send by the server to the client
	 * @throws ServletException if an error occurred
	 * @throws IOException if an error occurred
	 */
	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		doPost(request, response);
	}

	/**
	 * The doPost method of the servlet. <br>
	 *
	 * This method is called when a form has its tag value method equals to post.
	 * 
	 * @param request the request send by the client to the server
	 * @param response the response send by the server to the client
	 * @throws ServletException if an error occurred
	 * @throws IOException if an error occurred
	 */
	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		response.setHeader("Access-Control-Allow-Origin", "*");
		
		//wap页参数		
		
		String callback =  BlankUtils.checkNull(request, "jsoncallback");//跨域访问回调方法名
		
		String lsn = BlankUtils.checkNull(request, "lsn");//设备id
		String appVer = BlankUtils.checkNull(request, "appVer");//客户端应用版本号
		String url = BlankUtils.checkNull(request, "url");//当点击链接时取链接地址，但注意urlencode， 回传
		
		String device = "0";
		String channel = "youlike";
		String a = BlankUtils.checkNull(request, "a");//新闻自带，回传
		String c = BlankUtils.checkNull(request, "c");//新闻自带，回传
		String source = BlankUtils.checkNull(request, "source");//新闻自带，回传
		
		source = new String(source.getBytes("ISO8859-1"),"UTF-8");
		
		source = URLEncoder.encode(source, "utf-8");
		
		String t = String.valueOf(System.currentTimeMillis());
		String sid = BlankUtils.checkNull(request, "sid");//新闻自带，回传
		String scene = BlankUtils.checkNull(request, "scene");//场景标识，同一个渠道下可用来区分不同的新闻展现场景；流量入口的区分；
		String func = "click";
		String s = BlankUtils.checkNull(request, "s");//新闻自带，回传
		String act = "click";
		String style = BlankUtils.checkNull(request, "style");//新闻自带，回传
		
		String sign = QihooApiParam.SIGN;
		
		MD5 m = new MD5();
		String uid = "6b09f44a58db79719102ceb4a5528a87";//临时测试 uid
		
		if(lsn!=null){
			uid = m.getMD5ofStr(lsn).toLowerCase();
		}
		
		String version = "1.0.1";//临时测试
		
		if(appVer!=null){
			version = appVer;
		}
			
		String getUrl = "/srv/c?uid="+uid+"&url="+url+"&sign="+sign+"&version="+version+"&device="+device+"&channel="+channel
		+"&a="+a+"&c="+c+"&source="+source+"&t="+t+"&sid="+sid+"&scene="+scene+"&func="+func+"&s="+s+"&act="+act+"&style="+style;
		
//		System.out.println("QihooApi click url=>"+QihooApiParam.GETTOKENURL+getUrl);
		
		/*HttpMethod method = new GetMethod(getUrl);   
		String resDt = exeClienUrl(method, QihooApiParam.GETTOKENURL);*/
		String resDt = httpGet(QihooApiParam.GETTOKENURL+getUrl);
		
//		System.out.println("QihooApi click res=>"+resDt);
		
		OutputStream os = null;
		try {
			String ret = callback+"("+resDt+")";
			byte[] responseBytes = ret.getBytes("UTF-8");
			response.setContentLength(responseBytes.length);
			os = response.getOutputStream();
			os.write(responseBytes);
			os.flush(); 
			os.close();  
		} catch (Exception e) {
			e.printStackTrace();
		}finally
		{
			if(os!=null)
			{
				os.flush(); 
				os.close();  
			}
		}
	}
	
	public String httpGet(String url) {
    	String responseContent = null;
    	RequestConfig defaultRequestConfig = RequestConfig.custom()
    		    .setSocketTimeout(10000)
    		    .setConnectTimeout(10000)
    		    .setConnectionRequestTimeout(10000)
    		    .setStaleConnectionCheckEnabled(true)
    		    .build();
		CloseableHttpClient httpclient = HttpClients.custom()
		    .setDefaultRequestConfig(defaultRequestConfig).build();

        try {
            HttpGet httpGet = new HttpGet("http://"+url);
            httpGet.addHeader("Connection", "close");
            CloseableHttpResponse response = httpclient.execute(httpGet);  
            try {  
                HttpEntity entity = response.getEntity();
                responseContent = EntityUtils.toString(entity, "UTF-8");
            } finally {  
                response.close();  
            }  
        } catch (Exception e) {
            e.printStackTrace();
        } finally {  
            try {  
                httpclient.close();  
            } catch (IOException e) {  
                e.printStackTrace();  
            }  
        }  
        return responseContent;  
    }  

	/*private String exeClienUrl(HttpMethod method,String url){
		StringBuffer sb = new StringBuffer();
		long t1 = System.currentTimeMillis();
		//post.setRequestHeader("Content-Type","text/html");
		HttpClient client = new HttpClient();
		client.getHttpConnectionManager().getParams().setConnectionTimeout(10000); 
		client.getHttpConnectionManager().getParams().setSoTimeout(10000);
		client.getHostConfiguration().setHost(url, 80,
				"http");
		method.setRequestHeader("Connection", "close");
		InputStream inputStream = null;
		BufferedReader bre = null;
		try {
			client.executeMethod(method);		
			
			inputStream = method.getResponseBodyAsStream();  
			bre = new BufferedReader(new InputStreamReader(inputStream,"utf-8"));  
			String str= "";  
			while((str = bre.readLine()) != null){  
				sb .append(str);  
			} 
		} catch (HttpException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
		}finally{
			if(inputStream!=null){
				try {
					inputStream.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					//e.printStackTrace();
				}
			}
			if(bre!=null){
				try {
					bre.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					//e.printStackTrace();
				}
			}
			method.releaseConnection();
			client.getHttpConnectionManager().closeIdleConnections(0);
			long t2 = System.currentTimeMillis();
			
			Calendar c = Calendar.getInstance();  
		    c.setTimeInMillis(t2 - t1);  
		  
		    log.debug("耗时: " + c.get(Calendar.MINUTE) + "分 "  
		                + c.get(Calendar.SECOND) + "秒 " + c.get(Calendar.MILLISECOND)  
		                + " 毫秒"); 
		}
		//log.debug("api type=>"+url);
		//log.debug("api get res = >"+sb.toString());
		
		return sb.toString();
	}*/
	
	/**
	 * chc计算公式
	 * @param bytes
	 * @return
	 */
	private String getFormattedText(byte[] bytes) {
		  char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
		  int len = bytes.length;
		  StringBuilder buf = new StringBuilder(len * 2);
		  // 把密文转换成十六进制的字符串形式
		  for (int j = 0; j < len; j++) {
		    buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
		    buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
		  }
		  return buf.toString();
		}
	/**
	 * Initialization of the servlet. <br>
	 *
	 * @throws ServletException if an error occurs
	 */
	public void init() throws ServletException {
		// Put your code here
	}

}
